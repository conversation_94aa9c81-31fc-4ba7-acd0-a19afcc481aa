import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { throwError } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { LoaderService } from '../services/loader/loader.service';
import { Router } from '@angular/router';

export const LoaderInterceptor: HttpInterceptorFn = (request, next) => {
  const loaderService = inject(LoaderService);
  const router = inject(Router);

  const excludedEndpoints = [
    '/auth/login-url',
    '/auth/logout-url',
    '/auth/basic/refresh/token',
    '/ava/force/individualAgent/execute',
    '/force/platform/pipeline/api/v1/test_tool',
    '/ava/force/userTools',
    '/ava/force/model',
    '/ava/force/guardrail',
    '/auth/organization/hierarchy',
    'da/userTools?pag',
    '/ava/force/agent-execute',
    '/ava/force/agent-execute/files',
    '/ava/force/contents',
    '/auth/refresh-token',
    '/auth/roles',
    '/auth/pages',
    '/auth/use',
    '/api/auth/realms',
    '/api/auth/actions',
    '/api/auth/access/permissions',
    '/ava/force/workflow/execute'
  ];

  const excludedRoutes = [
    '/build/agents',
    '/libraries/prompts',
    '/libraries/prompts/create',
    '/libraries/models',
    '/libraries/models/create',
    '/libraries/tools',
    'libraries/tools?page',
    '/build/workflows',
    '/libraries/knowledge-base',
    '/libraries/knowledge-base/create',
    '/manage/admin-management',
    '/manage/admin-management/add-user',
    '/approval/approval-workflows',
    '/approval/approval-agents',
    '/approval/approval-tools',
  ];

  function isExcludedRoute(url: string): boolean {
    return excludedRoutes.some((route) => url === route);
  }

  function isExcludeRealmCondition(): boolean {
    return  request.url.includes('api/auth/realms') && router.url.includes('manage/realm-management');
  }

  const shouldExclude =
    excludedEndpoints.some((endpoint) => request.url.includes(endpoint)) ||
    isExcludedRoute(router.url) ||
    isExcludeRealmCondition();

  // Generate a unique request ID for tracking
  const requestId = `${request.url}|${Date.now()}|${Math.random().toString(36).substr(2, 9)}`;

  if (!shouldExclude) {
    loaderService.serviceStarted(requestId);
  }

  return next(request).pipe(
    catchError((error: HttpErrorResponse) => {
      return throwError(() => error);
    }),
    finalize(() => {
      if (!shouldExclude) {
        loaderService.serviceCompleted(requestId);
      }
    }),
  );
};
