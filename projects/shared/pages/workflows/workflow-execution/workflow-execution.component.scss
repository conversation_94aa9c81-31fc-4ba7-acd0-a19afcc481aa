$header-bg: #e9effd;
$main-color: #1a46a7;

::ng-deep .ava-tabs {
  background: none !important;
}

::ng-deep .ava-tabs__container {
  border-radius: none !important;
  border: none !important;
  padding: 0 !important;
  box-shadow: none !important;
  background: none !important;
}

::ng-deep .ava-tabs__list {
  padding: 0 !important;
}

.workflow-execution-container {
  display: flex;
  flex-direction: column;
  height: 93vh;
  overflow: hidden;
  .execution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    flex-shrink: 0;

    .execution-title {
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color, #333);
      }

      .header-buttons {
        display: flex;
        gap: 12px;
      }
    }

    .execution-actions {
      display: flex;
      gap: 12px;

      .back-button,
      .edit-button {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 5px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        svg {
          margin-right: 6px;
        }
      }

      .back-button {
        background-color: var(--bg-muted, #f5f5f5);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);

        &:hover {
          background-color: var(--bg-muted-hover, #e9e9e9);
        }
      }

      .edit-button {
        background-color: var(--card-bg, #fff);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);

        &:hover {
          background-color: var(--card-bg-hover, #f9f9f9);
          border-color: var(--border-color-dark, #d0d0d0);
        }
      }
    }
  }

  // Main content with 2 panels (left input panel + right results panel)
  .execution-content {
    display: flex;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    gap: 20px;
    padding: 20px;

    // Left Panel: Workflow Playground with Execute Button
    .left-panel {
      flex: 2.5;
      display: flex;
      flex-direction: column;
      min-width: 0;
      background: transparent;
      transition: all 0.3s ease;

      &.collapsed {
        flex: 0;
        min-width: 3%;
        width: 3%;
        overflow: hidden;
      }

      .playground-component {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
      }

      .execute-button-container {
        padding: 16px;
        background-color: var(--card-bg, white);
        border-radius: 0 0 8px 8px;
        border-top: 1px solid var(--border-color, #e0e0e0);
        flex-shrink: 0;

        ::ng-deep .ava-button {
          width: 100% !important;
          min-height: 48px;
          font-weight: 600;
          font-size: 16px;
        }

        ::ng-deep .ava-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    // Right Panel: Three Sections with Tabs
    .right-panel {
      flex: 5.5;
      display: flex;
      flex-direction: column;
      background-color: var(--card-bg, white);
      border-radius: 8px;
      box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));
      overflow: hidden;
      transition: all 0.3s ease;

      &.expanded {
        flex: 8;
      }

      // Tab Navigation Header
      .tabs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        background: #E6F3FF;
        height: 64px;
        flex-shrink: 0;

        .tabs-container {
          flex: 1;
        }

        .header-actions {
          flex-shrink: 0;
          margin-left: 16px;
        }
      }

      // Content Sections
      .content-sections {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .section-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          &.execution-section {
            display: flex;
            flex-direction: column;

            .execution-monitor-header {
              padding: 20px 24px;
              border-bottom: 1px solid var(--border-color, #e0e0e0);
              background-color: var(--card-bg, white);
              flex-shrink: 0;

              .monitor-title {
                font-size: 18px;
                font-weight: 600;
                color: var(--text-color, #333);
                margin-bottom: 16px;
              }

              h2 {
                margin: 0 0 16px 0;
                font-size: 20px;
                font-weight: 600;
                color: var(--text-color, #333);
              }

              .progress-section {
                .progress-label {
                  font-size: 14px;
                  color: var(--text-muted, #666);
                  margin-bottom: 8px;
                  display: block;
                }

                .progress-container {
                  display: flex;
                  align-items: center;
                  gap: 12px;

                  .progress-bar {
                    flex: 1;
                    height: 8px;
                    background-color: var(--bg-muted, #f0f0f0);
                    border-radius: 4px;
                    overflow: hidden;

                    .progress-fill {
                      height: 100%;
                      background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);
                      border-radius: 4px;
                      transition: width 0.3s ease;
                    }
                  }

                  .progress-text {
                    font-size: 14px;
                    font-weight: 600;
                    color: var(--text-color, #333);
                    min-width: 40px;
                  }
                }
              }
            }

            .execution-content-grid {
              display: flex;
              flex: 1;
              min-height: 0;
              gap: 16px;
              padding: 20px;
              background-color: var(--card-bg, white);

              .pipeline-steps-section,
              .execution-logs-section {
                flex: 1;
                min-height: 0;
                background-color: #F7F8F9;
                border-radius: 16px;
                display: flex;
                flex-direction: column;
                overflow: hidden;
              }

              .pipeline-steps-section {
                pointer-events: none;
                opacity: 0.5;
                filter: blur(1px);
                h3 {
                  margin: 0;
                  padding: 20px 24px 16px 24px;
                  font-size: 18px;
                  font-weight: 600;
                  color: var(--text-color, #333);
                  background-color: #F7F8F9;
                  flex-shrink: 0;
                  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                }

                .pipeline-agents-list {
                  flex: 1;
                  min-height: 0;
                  padding: 0 24px 24px 24px;
                  background-color: #F7F8F9;
                  overflow-y: auto;

                  .pipeline-agent-item {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    padding: 16px 20px;
                    margin-bottom: 12px;
                    border: 1px solid #E5E7EB;
                    border-radius: 16px;
                    background-color: white;
                    transition: all 0.2s ease;

                    &.active {
                      border-color: #6566cd;
                      background-color: white;
                      box-shadow: 0 2px 8px rgba(101, 102, 205, 0.1);
                    }

                    &.completed {
                      border-color: #10b981;
                      background-color: white;
                      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
                    }

                    .agent-icon {
                      width: 40px;
                      height: 40px;
                      border-radius: 50%;
                      background-color: #F3F4F6;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                      border: 2px solid white;
                    }

                    .agent-info {
                      flex: 1;
                      display: flex;
                      flex-direction: column;

                      .agent-name {
                        font-size: 16px;
                        font-weight: 600;
                        color: var(--text-color, #333);
                        margin-bottom: 4px;
                      }

                      .agent-status {
                        font-size: 14px;
                        color: #6B7280;
                      }
                    }
                  }
                }
              }

              .execution-logs-section {
                display: flex;
                flex-direction: column;
                height: 100%;
                background-color: #F7F8F9;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

                .logs-header {
                  padding: 16px 20px;
                  background-color: #F7F8F9;
                  border-bottom: 1px solid #E5E7EB;
                  position: sticky;
                  top: 0;
                  z-index: 10;
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);

                  h3 {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: #111827;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  }

                  .logs-subtitle {
                    font-size: 12px;
                    color: #6B7280;
                    margin-top: 4px;
                  }
                }

                .logs-content {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  min-height: 0;
                  background-color: #FFFFFF;
                  border-radius: 0 0 8px 8px;

                  .logs-scroll-container {
                    flex: 1;
                    overflow-y: auto;
                    padding: 12px 20px;
                    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                    font-size: 13px;
                    line-height: 1.5;
                    scrollbar-width: thin;
                    scrollbar-color: #D1D5DB #F3F4F6;

                    &::-webkit-scrollbar {
                      width: 6px;
                      height: 6px;
                    }

                    &::-webkit-scrollbar-track {
                      background: #F3F4F6;
                      border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                      background-color: #D1D5DB;
                      border-radius: 3px;
                    }

                    .log-entry {
                      padding: 4px 0;
                      margin: 0;
                      border-bottom: 1px solid #F3F4F6;

                      &:last-child {
                        border-bottom: none;
                      }

                      .log-message {
                        display: block;
                        word-break: break-word;
                        white-space: pre-wrap;
                        color: #374151;
                        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                        font-size: 13px;
                        line-height: 1.5;
                      }
                    }
                  }

                  .logs-footer {
                    padding: 12px 20px;
                    border-top: 1px solid #E5E7EB;
                    background-color: #FFFFFF;
                    text-align: center;

                    .show-more-btn {
                      background: none;
                      border: 1px solid #E5E7EB;
                      color: #4B5563;
                      font-size: 12px;
                      font-weight: 500;
                      cursor: pointer;
                      padding: 6px 16px;
                      border-radius: 6px;
                      transition: all 0.2s ease;
                      text-decoration: none;

                      &:hover {
                        background-color: #F9FAFB;
                        border-color: #D1D5DB;
                        color: #111827;
                      }

                      &:active {
                        background-color: #F3F4F6;
                      }
                    }
                  }
                }
              }
            }
          }

          &.output-section {
            app-agent-output {
              flex: 1;
              display: flex;
              flex-direction: column;
            }
          }

          &.configuration-section {
            padding: 0;
            overflow: hidden;
            height: 100%;

            .configuration-content {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
              text-align: center;

              p {
                color: var(--text-color, #666);
                font-size: 16px;
                margin: 0;
                font-style: italic;
              }
            }
          }
        }
      }
    }

  }
}
::ng-deep nav.ava-tabs__list {
  background: $header-bg;
  padding: 4px;
}

::ng-deep button.ava-button.primary.active {
  background: #616161;
  color: #fff;
}

::ng-deep .column-header .ava-tabs[data-variant="button"] .ava-tabs__tab--pill {
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-family: "Mulish";
}

::ng-deep
  .ava-tabs[data-variant="button"]
  .ava-tabs__tab--active
  .ava-tabs__tab-text {
  color: white;
}

::ng-deep .ava-tabs__tab-text {
  color: #4c515b;
  font-family: "Mulish";
  font-weight: 600;
}

::ng-deep .right-section-header .ava-button.secondary {
  color: #1a46a7;
  border: none;
}

::ng-deep .right-section-header .ava-button.secondary:hover {
  color: #1a46a7;
  border: none;
}

.right-section-header {
  text-align: end;
}

// Responsive design for workflow execution
@media (max-width: 1024px) {
  .workflow-execution-container {
    .execution-content {
      gap: 16px;
      height: 94vh;
    }
  }
}

@media (max-width: 768px) {
  .workflow-execution-container {
    .execution-header {
      padding: 16px 20px;

      .execution-title h1 {
        font-size: 20px;
      }
    }

    .execution-content {
      flex-direction: column;
      gap: 12px;
      padding: 12px;

      .left-panel {
        flex: 1;
        min-height: 400px;
      }

      .right-panel {
        flex: 1;
        min-height: 300px;

        .execution-content-grid {
          flex-direction: column;
          gap: 12px;
          padding: 16px;

          .pipeline-steps-section,
          .execution-logs-section {
            flex: 1;
            min-height: 200px;
            border-radius: 16px;
            background-color: #F7F8F9;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .workflow-execution-container {
    .execution-header {
      padding: 12px 16px;

      .execution-title h1 {
        font-size: 18px;
      }

      .execution-actions {
        gap: 8px;

        .back-button,
        .edit-button {
          padding: 8px 12px;
          font-size: 14px;
        }
      }
    }

    .execution-content {
      gap: 8px;
      padding: 8px;

      .left-panel {
        flex: 1;
        min-height: 350px;
      }

      .right-panel {
        flex: 1;
        min-height: 250px;

        .execution-content-grid {
          flex-direction: column;
          gap: 12px;
          padding: 12px;

          .pipeline-steps-section,
          .execution-logs-section {
            flex: 1;
            min-height: 180px;
            border-radius: 16px;
            background-color: #F7F8F9;

            .pipeline-agents-list,
            .logs-content {
              padding: 12px 16px;
            }

            h3 {
              padding: 16px 20px 12px 20px;
            }
          }
        }

        .execution-monitor-header {
          padding: 16px 20px;
        }
      }
    }
  }
}
