import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild, ElementRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '@ava/play-comp-library';
import { AgentData, AgentInput } from '../workflow-playground/workflow-playground.component';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-agent-stepper-card',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconComponent
  ],
  templateUrl: './agent-stepper-card.component.html',
  styleUrls: ['./agent-stepper-card.component.scss'],
  animations: [
    trigger('slideDown', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class AgentStepperCardComponent {
  @Input() agent!: AgentData;
  @Input() stepNumber: number = 1;
  @Input() isFirst: boolean = false;
  @Input() isLast: boolean = false;
  @Input() isActive: boolean = false;
  @Input() isCompleted: boolean = false;
  
  @Output() inputChanged = new EventEmitter<{inputIndex: number, value: string}>();
  @Output() fileSelected = new EventEmitter<{inputIndex: number, files: File[]}>();
  @Output() messageSent = new EventEmitter<{inputIndex: number, value: string, files?: File[]}>();
  @Output() stepCompleted = new EventEmitter<void>();

  @ViewChild('fileInput') fileInput!: ElementRef;

  isExpanded: boolean = false;
  showAllInputs: boolean = false;
  maxVisibleInputs: number = 2;
  
  // Tracks which inputs have been completed
  completedInputs: Set<number> = new Set();

  get visibleInputs(): AgentInput[] {
    if (!this.agent.inputs) return [];
    if (this.showAllInputs || this.agent.inputs.length <= this.maxVisibleInputs) {
      return this.agent.inputs;
    }
    return this.agent.inputs.slice(0, this.maxVisibleInputs);
  }

  get hiddenInputsCount(): number {
    if (!this.agent.inputs) return 0;
    return Math.max(0, this.agent.inputs.length - this.maxVisibleInputs);
  }

  get stepperClass(): string {
    const classes = ['stepper-circle'];
    if (this.isCompleted) classes.push('completed');
    else if (this.isActive) classes.push('active');
    else classes.push('inactive');
    return classes.join(' ');
  }

  toggleExpanded(): void {
    if (this.agent.hasInputs) {
      this.isExpanded = !this.isExpanded;
    }
  }

  toggleShowAllInputs(): void {
    this.showAllInputs = !this.showAllInputs;
  }

  onInputChange(inputIndex: number, event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.inputChanged.emit({ inputIndex, value: target.value });
  }

  onFileInputClick(inputIndex: number): void {
    // Store the input index for file selection
    this.fileInput.nativeElement.dataset['inputIndex'] = inputIndex.toString();
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    const inputIndex = parseInt(target.dataset['inputIndex'] || '0');
    
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      
      // Update the local files first for immediate UI update
      if (this.agent.inputs && this.agent.inputs[inputIndex]) {
        this.agent.inputs[inputIndex].files = fileArray;
        
        // For image inputs, automatically send the image
        if (this.agent.inputs[inputIndex].inputType === 'image') {
          this.handleSendMessage(inputIndex);
        } else {
          // For non-image files, just emit the file selection
          this.fileSelected.emit({ inputIndex, files: fileArray });
          this.completedInputs.add(inputIndex);
          this.checkAllInputsCompleted();
        }
      }
    }
    
    // Reset the file input to allow selecting the same file again if needed
    target.value = '';
  }

  removeFile(inputIndex: number, fileIndex: number, event?: Event): void {
    event?.stopPropagation();
    
    if (this.agent.inputs && this.agent.inputs[inputIndex]?.files?.length) {
      // Clear the files array
      this.agent.inputs[inputIndex].files = [];
      
      // Emit empty files to clear the selection
      this.fileSelected.emit({ inputIndex, files: [] });
      
      // If this was an image input, emit a message with empty files
      if (this.agent.inputs[inputIndex].inputType === 'image') {
        this.messageSent.emit({
          inputIndex,
          value: '',
          files: []
        });
      }
      
      // Update completion status
      this.completedInputs.delete(inputIndex);
      this.checkAllInputsCompleted();
    }
  }

  handleSendMessage(inputIndex: number): void {
    if (this.agent.inputs && this.agent.inputs[inputIndex]) {
      const input = this.agent.inputs[inputIndex];
      const inputValue = input.value || '';
      const inputFiles = input.files ? [...input.files] : undefined;
      
      // For image inputs, require a file to be uploaded
      if (input.inputType === 'image' && (!inputFiles || inputFiles.length === 0)) {
        return; // Don't proceed if no file is selected for image input
      }
      
      // For text inputs, require some content if it's not optional
      if (input.inputType === 'text' && !inputValue && !input.isOptional) {
        return; // Don't proceed if text input is required but empty
      }
      
      // Emit the message with the current values
      this.messageSent.emit({
        inputIndex,
        value: inputValue,
        files: inputFiles
      });
      
      // Only clear the input field if it's not the last input
      const isLastInput = inputIndex === this.agent.inputs.length - 1;
      
      if (input.inputType === 'text') {
        // Don't clear the input field, just mark as completed
        this.completedInputs.add(inputIndex);
      } else if (input.inputType === 'image' || input.inputType === 'file') {
        // Keep the files for reference but mark as completed
        this.completedInputs.add(inputIndex);
      }
      
      // Check if all required inputs are completed
      this.checkAllInputsCompleted();
      
      // Auto-close the card after input is entered
      setTimeout(() => {
        this.isExpanded = false;
      }, 500); // Small delay to show the input was processed
      
      // If this is not the last input, focus the next input
      if (!isLastInput && input.inputType === 'text') {
        // Find the next text input and focus it
        const nextIndex = inputIndex + 1;
        if (nextIndex < this.agent.inputs.length) {
          // Small delay to ensure the DOM is updated
          setTimeout(() => {
            const nextInput = document.querySelector(`.input-textarea[data-index="${nextIndex}"]`) as HTMLTextAreaElement;
            if (nextInput) {
              nextInput.focus();
            }
          }, 100);
        }
      }
    }
  }

  getAcceptedFileType(input: AgentInput): string {
    return input.inputType === 'image' 
      ? '.png,.jpg,.jpeg,.gif,.bmp,.svg'
      : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';
  }

  isInputDisabled(input: AgentInput): boolean {
    // Never disable inputs - user should always be able to enter input
    return false;
  }
  
  showFileUploadButton(input: AgentInput): boolean {
    // Only show file upload for file or image inputs
    return input.inputType === 'file' || input.inputType === 'image';
  }
  
  checkAllInputsCompleted(): void {
    if (!this.agent.inputs) {
      this.stepCompleted.emit();
      return;
    }
    
    // Check if all required inputs are completed
    const allRequiredInputsCompleted = this.agent.inputs.every((input) => {
      // Skip optional inputs
      if (input.isOptional) return true;
      
      // For image inputs, check if files are uploaded
      if (input.inputType === 'image') {
        return !!input.files?.length;
      }
      
      // For text inputs, check if there's a value
      if (input.inputType === 'text') {
        return !!input.value?.trim();
      }
      
      // For file inputs, check if files are uploaded
      if (input.inputType === 'file') {
        return !!input.files?.length;
      }
      
      // Default to true for any other input types
      return true;
    });
    
    if (allRequiredInputsCompleted) {
      this.stepCompleted.emit();
    }
  }

  trackByIndex(index: number, item: any): number {
    return index;
  }
}
