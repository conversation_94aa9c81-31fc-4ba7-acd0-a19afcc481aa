import { Component, Input, OnInit, OnChanges, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@ava/play-comp-library';
import { CanvasBoardComponent, CanvasNode, CanvasEdge } from '@shared/components/canvas-board/canvas-board.component';
import { AgentNodeComponent } from '../../../workflow-editor/components/agent-node/agent-node.component';
import { WorkflowNode, WorkflowEdge } from '../../../workflow-editor/services/workflow-graph.service';

@Component({
  selector: 'app-workflow-configuration',
  standalone: true,
  imports: [
    CommonModule,
    AgentNodeComponent,
    IconComponent,
    CanvasBoardComponent
  ],
  templateUrl: './workflow-configuration.component.html',
  styleUrls: ['./workflow-configuration.component.scss']
})
export class WorkflowConfigurationComponent implements OnInit, OnChanges {
  @Input() workflowData: any = null;
  @Input() workflowName: string = 'Workflow';
  @ViewChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;

  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  
  // Canvas board configuration - read-only mode
  showGrid: boolean = true;
  enablePan: boolean = true;
  enableZoom: boolean = true;
  enableConnections: boolean = false; // Read-only, no new connections
  showToolbar: boolean = false; // Hide toolbar in read-only mode
  mouseInteractionsEnabled: boolean = false; // Disable interactions
  fallbackMessage: string = 'No workflow configuration available';
  navigationHints: string[] = [
    'This is a read-only view of your workflow configuration',
    'Use mouse wheel to zoom',
    'Drag to pan around the canvas'
  ];

  ngOnInit(): void {
    if (this.workflowData) {
      this.processWorkflowData();
    }
  }

  ngOnChanges(): void {
    if (this.workflowData) {
      this.processWorkflowData();
    }
  }

  private processWorkflowData(): void {
    if (!this.workflowData?.pipeLineAgents) {
      return;
    }

    // Convert pipeline agents to canvas nodes
    this.canvasNodes = this.workflowData.pipeLineAgents.map((pipelineAgent: any, index: number) => {
      const agent = pipelineAgent.agent;
      return {
        id: `agent-${agent.id || index}`,
        type: 'agent',
        position: { 
          x: 100 + (index * 350), // Horizontal layout with spacing
          y: 100 
        },
        data: {
          // Fields expected by app-agent-node component
          label: agent.name || `Agent ${index + 1}`,
          description: agent.task?.description || agent.goal || 'No description available',
          model: agent.llm?.model || agent.llm?.modelDeploymentName || 'No model specified',
          tools: agent.tools || [],
          agentTools: agent.tools || [],
          
          // Additional fields for display
          agentId: agent.id || `agent-${index}`,
          name: agent.name || `Agent ${index + 1}`,
          role: agent.role || 'Agent',
          goal: agent.goal || '',
          backstory: agent.backstory || '',
          task: {
            description: agent.task?.description || '',
            expectedOutput: agent.task?.expectedOutput || ''
          },
          serial: pipelineAgent.serial || (index + 1),
          llm: agent.llm || null
        }
      };
    });

    // Create edges to connect agents in sequence
    this.canvasEdges = [];
    for (let i = 0; i < this.canvasNodes.length - 1; i++) {
      this.canvasEdges.push({
        id: `edge-${i}`,
        source: this.canvasNodes[i].id,
        target: this.canvasNodes[i + 1].id,
        animated: false
      });
    }
  }

  // No event handlers needed for read-only configuration view
}
