.agent-output {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background: var(--card-bg);

  .output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--text-color);
    }
  }

  .output-content {
    flex-grow: 1;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--dashboard-scrollbar-track);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--dashboard-scrollbar-thumb);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--dashboard-scrollbar-thumb-hover);
    }

    .no-outputs {
      padding: 40px 0;
      text-align: center;
      color: var(--text-secondary);
      font-size: 14px;
      font-style: italic;
    }

    .output-card {
      margin-bottom: 16px;
      border: 1px solid var(--dashboard-border-light);
      border-radius: 8px;
      overflow: hidden;
      background-color: var(--card-bg);
      box-shadow: 0 2px 4px var(--card-shadow);
      transition: all 0.3s ease;

      &.expanded {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .output-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        background-color: var(--dashboard-bg-lighter);
        border-bottom: 1px solid var(--dashboard-border-light);

        &:hover {
          background-color: rgba(0, 0, 0, 0.02);
        }

        .header-content {
          flex: 1;
          margin-right: 12px;
          min-width: 0; // Important for text truncation

          .output-title {
            margin: 0 0 4px 0;
            font-size: 15px;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 8px;
            
            // Truncate long titles to single line with ellipsis
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: calc(100% - 100px); // Leave space for agent name and actions

            .agent-name {
              font-weight: 500;
              color: var(--text-secondary);
              font-size: 13px;
              flex-shrink: 0; // Prevent agent name from being truncated
            }
          }

          .output-subtitle {
            .timestamp {
              font-size: 12px;
              color: var(--text-tertiary);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }

        .header-actions {
          flex-shrink: 0; // Prevent toggle button from shrinking

          .action-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            transition: all 0.2s ease;
            min-width: 32px;
            min-height: 32px;

            &:hover {
              background-color: rgba(0, 0, 0, 0.05);
              color: var(--text-color);
            }

            ava-icon {
              transition: all 0.3s ease;
              
              &.expanded {
                transform: scale(1.1);
              }
            }
          }
        }
      }

      .output-card-content {
        overflow: hidden;
        transition: all 0.3s ease;
        background-color: var(--card-bg);
        
        // Default collapsed state
        max-height: 0;
        opacity: 0;
        padding: 0 16px;

        .output-details {
          padding: 16px 0;
          max-height: 400px; // Set max height for scrolling
          overflow-y: auto; // Enable vertical scrolling
          
          // Custom scrollbar styling
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: var(--dashboard-scrollbar-track);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: var(--dashboard-scrollbar-thumb);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb:hover {
            background: var(--dashboard-scrollbar-thumb-hover);
          }

          .output-section {
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            h5 {
              margin: 0 0 8px 0;
              font-size: 13px;
              font-weight: 600;
              color: var(--text-secondary);
              text-transform: uppercase;
              letter-spacing: 0.5px;

              strong {
                font-weight: 700;
                color: var(--text-color);
              }
            }

            p {
              margin: 0;
              font-size: 14px;
              line-height: 1.5;
              color: var(--text-color);
              word-wrap: break-word;
            }
          }

          .output-raw {
            margin-top: 24px;
            border-top: 1px solid var(--dashboard-border-light);
            padding-top: 16px;

            .raw-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              h5 {
                margin: 0;
                font-size: 13px;
                font-weight: 600;
                color: var(--text-secondary);
                text-transform: uppercase;
                letter-spacing: 0.5px;

                strong {
                  font-weight: 700;
                  color: var(--text-color);
                }
              }

              .copy-button {
                display: flex;
                align-items: center;
                gap: 6px;
                background: none;
                border: 1px solid var(--dashboard-border-light);
                border-radius: 4px;
                padding: 6px 10px;
                font-size: 12px;
                color: var(--text-secondary);
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background-color: var(--dashboard-bg-lighter);
                  color: var(--text-color);
                }

                svg {
                  width: 14px;
                  height: 14px;
                }
              }
            }

            pre {
              margin: 0;
              padding: 12px;
              background-color: var(--code-bg, #f8f9fa);
              border-radius: 4px;
              overflow-x: auto;
              font-family: 'Fira Code', monospace;
              font-size: 13px;
              line-height: 1.5;
              color: var(--code-color, #333);
              max-height: 300px;
              overflow-y: auto;

              &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
              }

              &::-webkit-scrollbar-track {
                background: var(--dashboard-scrollbar-track);
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: var(--dashboard-scrollbar-thumb);
                border-radius: 3px;
              }

              code {
                font-family: inherit;
                word-wrap: break-word;
                white-space: pre-wrap;
              }
            }
          }
        }
      }

      // Expanded state styles
      &.expanded .output-card-content {
        max-height: 2000px;
        opacity: 1;
        padding: 0 16px 16px 16px;
      }
    }
  }
}