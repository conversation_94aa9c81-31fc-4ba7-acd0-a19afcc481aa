<div class="agent-output">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>

  <div class="output-header">
    <h3 id="agent-output-title">Agent Output</h3>
    <ava-button
      (userClick)="exportOutput()"
      aria-label="Export agent outputs"
      title="Export agent outputs"
      variant="primary"
      size="medium"
      label="Export"
      [iconName]="'Download'"
      [iconPosition]="'right'"
      [iconColor]="'white'"
    >
    </ava-button>
  </div>

  <div class="output-content" aria-labelledby="agent-output-title">
    <!-- No outputs message -->
    <div *ngIf="!outputs.length" class="no-outputs" aria-live="polite">
      No agent outputs available yet.
    </div>

    <!-- Output cards with collapsible content -->
    <div *ngFor="let output of outputs" class="output-card" [class.expanded]="expandedStates[output.id]">
      <div class="output-card-header" (click)="toggleExpand(output.id, $event)">
        <div class="header-content">
          <h4 class="output-title">
            {{ output.title || 'Output' }}
            <span class="agent-name">({{ output.agentName || 'Agent' }})</span>
          </h4>
          <div class="output-subtitle">
            <span class="timestamp">{{ output.timestamp | date:'medium' }}</span>
          </div>
        </div>
        <div class="header-actions">
          <button 
            class="action-button" 
            (click)="toggleExpand(output.id, $event)" 
            [title]="expandedStates[output.id] ? 'Collapse details' : 'Expand details'"
            [attr.aria-expanded]="expandedStates[output.id]"
          >
            <ava-icon
              [class.expanded]="expandedStates[output.id]"
              [iconName]="expandedStates[output.id] ? 'chevron-up' : 'chevron-down'"
              [iconSize]="16"
              [title]="expandedStates[output.id] ? 'Collapse details' : 'Expand details'"
              iconColor="#444653">
            </ava-icon>
          </button>
        </div>
      </div>
      
      <div class="output-card-content">
        <div class="output-details">
          <div class="output-section" *ngIf="output.description">
            <h5><strong>Description</strong></h5>
            <p>{{ output.description }}</p>
          </div>
          
          <div class="output-section" *ngIf="output.expected_output">
            <h5><strong>Expected Output</strong></h5>
            <p>{{ output.expected_output }}</p>
          </div>
          
          <div class="output-raw" *ngIf="output.raw">
            <div class="raw-header">
              <h5><strong>Raw Output</strong></h5>
              <button class="copy-button" (click)="copyToClipboard(output.raw)" title="Copy to clipboard">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="9" y="9" width="13" height="13" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012 2v1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Copy
              </button>
            </div>
            <pre><code>{{ output.raw | json }}</code></pre>
          </div>
          
          <div class="output-section" *ngIf="output.summary">
            <h5><strong>Summary</strong></h5>
            <p>{{ output.summary }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>