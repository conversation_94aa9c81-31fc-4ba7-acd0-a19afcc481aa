import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule, JsonPipe } from '@angular/common';
import { ButtonComponent,IconComponent } from '@ava/play-comp-library';
export interface AgentOutput {
  id: string;
  title: string;
  content: string;
  agentName: string;
  timestamp: string;
  type?: 'code' | 'text' | 'json' | 'markdown';
  description: string;
  expected_output: string;
  summary: string;
  raw: string;
  [key: string]: any; // Allow additional properties
}

@Component({
  selector: 'app-agent-output',
  standalone: true,
  imports: [CommonModule, ButtonComponent, JsonPipe,IconComponent],
  templateUrl: './agent-output.component.html',
  styleUrls: ['./agent-output.component.scss'],
})
export class AgentOutputComponent implements OnInit {
  @Input() outputs: AgentOutput[] = [];
  @Output() export = new EventEmitter<void>();
  
  // Track expanded state for each output
  expandedStates: {[key: string]: boolean} = {};

  constructor() {}

  ngOnInit(): void {
    // Initialize expanded state for each output
    this.outputs.forEach(output => {
      this.expandedStates[output.id] = false;
    });
  }

  toggleExpand(id: string, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.expandedStates[id] = !this.expandedStates[id];
  }

  getContentType(output: AgentOutput): string {
    // Default to 'text' if not specified
    if (!output.type) {
      // Try to auto-detect
      if (output.content.startsWith('<') && output.content.includes('</')) {
        return 'code';
      } else if (
        output.content.includes('```') ||
        output.content.includes('#')
      ) {
        return 'markdown';
      } else if (
        output.content.startsWith('{') ||
        output.content.startsWith('[')
      ) {
        return 'json';
      }
      return 'text';
    }
    return output.type;
  }

  copyToClipboard(content: string): void {
    navigator.clipboard
      .writeText(content)
      .then(() => {
        // Could show a toast notification here
        console.log('Content copied to clipboard');
      })
      .catch((err) => {
        console.error('Could not copy text: ', err);
      });
  }

  exportOutput(): void {
    this.export.emit();
  }

  previewOutput(output: AgentOutput): void {
    // In a real app, this would open a preview modal or navigate to a preview page
    console.log('Preview output:', output);
    window.open(
      `data:text/html;charset=utf-8,${encodeURIComponent(output.content)}`,
      '_blank',
    );
  }
}
