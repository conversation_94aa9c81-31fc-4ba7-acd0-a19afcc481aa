.workflow-playground-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));
  transition: all 0.3s ease;
  overflow: hidden;

  &.collapsed {
    width: 60px;
    min-width: 60px;
  }
}

.playground-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #E6F3FF;
  color: #000000;
  min-height: 64px;
  box-sizing: border-box;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;

    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border: none;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      flex-shrink: 0;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateX(-2px);
      }

      &:active {
        transform: translateX(-1px);
      }
    }

    .workflow-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: white;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
    }
  }

  .collapse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.playground-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// Generating loader styles
.generating-indicator {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 5px 20px;
  background: var(--color-background-primary);
  // border-top: 1px solid var(--color-border-primary);
  // border-bottom: 1px solid var(--color-border-primary);

  .generating-stepper {
    display: flex;
    align-items: center;
    gap: 8px;

    .modern-loading-spinner {
      width: 40px;
      height: 40px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .spinner-ring {
        position: absolute;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2.5px solid transparent;
        border-top-color: #0084ff;
        border-bottom-color: #03bdd4;
        filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));
        animation: spin-ring 1.5s ease-in-out infinite;
      }

      .spinner-core {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: linear-gradient(135deg, #0084ff 0%, #03bdd4 100%);
        box-shadow: 0 0 8px rgba(176, 217, 255, 0.5);
        animation: pulse 1.5s ease-in-out infinite alternate;
      }
    }

    .generating-text {
      font-size: 16px;
      color: black;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      // background: linear-gradient(
      //   90deg,
      //   var(--color-text-secondary, #666) 25%,
      //   var(--code-viewer-bg) 50%,
      //   var(--color-text-secondary, #666) 75%
      // );
      // background-size: 200% 100%;
      // background-clip: text;
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
      animation: minimal-text-shine 1.5s ease-in-out infinite;
    }
  }

  // Dark theme support
  &.dark {
    .generating-stepper {
      .generating-text {
        color: var(--color-text-secondary-dark, #ccc);

        background: linear-gradient(
          90deg,
          var(--color-text-secondary-dark, #ccc) 25%,
          var(--code-viewer-bg) 50%,
          var(--color-text-secondary-dark, #ccc) 75%
        );
        background-size: 200% 100%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

// Animations
@keyframes spin-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.agents-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track, #f1f1f1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb, #c1c1c1);
    border-radius: 3px;

    &:hover {
      background: var(--scrollbar-thumb-hover, #a8a8a8);
    }
  }
}

.collapsed-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 10px;

  .collapsed-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;

    .collapsed-text {
      font-size: 12px;
      color: var(--text-secondary);
      writing-mode: vertical-rl;
      text-orientation: mixed;
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .workflow-playground-container {
    &.collapsed {
      width: 55px;
      min-width: 55px;
    }
  }

  .playground-header {
    .header-left {
      .workflow-title {
        font-size: 17px;
      }
    }
  }

  .agents-container {
    padding: 18px;
  }
}

@media (max-width: 768px) {
  .workflow-playground-container {
    &.collapsed {
      width: 50px;
      min-width: 50px;
    }
  }

  .playground-header {
    padding: 12px 16px;
    min-height: 56px;

    .header-left {
      gap: 8px;

      .back-btn,
      .collapse-btn {
        width: 32px;
        height: 32px;
      }

      .workflow-title {
        font-size: 16px;
      }
    }
  }

  .agents-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .workflow-playground-container {
    &.collapsed {
      width: 45px;
      min-width: 45px;
    }
  }

  .playground-header {
    padding: 10px 12px;
    min-height: 52px;

    .header-left {
      gap: 6px;

      .back-btn,
      .collapse-btn {
        width: 28px;
        height: 28px;
      }

      .workflow-title {
        font-size: 14px;
      }
    }
  }

  .agents-container {
    padding: 12px;
  }

  .collapsed-content {
    padding: 16px 8px;

    .collapsed-info {
      gap: 6px;

      .collapsed-text {
        font-size: 11px;
      }
    }
  }
}
