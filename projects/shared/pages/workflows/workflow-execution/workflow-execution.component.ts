import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';

// Import child components
import { ChatMessage } from '@shared/components/chat-window/chat-window.component';
import {
  AgentActivityComponent,
} from './components/agent-activity/agent-activity.component';
import {
  AgentOutputComponent,
  AgentOutput as OutputItem,
} from './components/agent-output/agent-output.component';
import {
  ButtonComponent,
  IconComponent,
  TabItem,
  TabsComponent,
} from '@ava/play-comp-library';
import { WorkflowService } from '@shared/services/workflow.service';
import { WorkflowInputExtractorService } from '@shared/services/workflow-input-extractor.service';
import { environment } from '@shared/environments/environment';
import workflowConstants from './../constants/workflows.json';
import { TokenStorageService, LoaderService } from '@shared/index';
import { AvaTab } from '@shared/models/tab.model';
import { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';
import { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';
import { WorkflowPlaygroundComponent, AgentData } from './components/workflow-playground/workflow-playground.component';
import { WorkflowConfigurationComponent } from './components/workflow-configuration/workflow-configuration.component';


@Component({
  selector: 'app-workflow-execution',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AgentActivityComponent,
    AgentOutputComponent,
    TabsComponent,
    ButtonComponent,
    IconComponent,
    WorkflowPlaygroundComponent,
    WorkflowConfigurationComponent,
  ],
  templateUrl: './workflow-execution.component.html',
  styleUrls: ['./workflow-execution.component.scss'],
})
export class WorkflowExecutionComponent implements OnInit, OnDestroy {
  navigationTabs: TabItem[] = [
    { id: 'nav-home', label: 'Execution' },
    { id: 'nav-products', label: 'Output' },
    { id: 'nav-services', label: 'Configuration' },
  ];
  // Workflow details
  workflowId: string | null = null;
  workflowName: string = 'Workflow';

  constants = workflowConstants as Record<string, any>;


  // Activity logs
  activityLogs: ActivityLog[] = [];
  activityProgress: number = 0;
  executionDetails?: AgentActivityExecutionDetails;
  isRunning: boolean = false;
  status: ExecutionStatus = ExecutionStatus.notStarted;

  // Chat messages
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  inputText = '';

  // Agent outputs
  agentOutputs: OutputItem[] = [];
  public workflowForm!: FormGroup;
  public fileType : string = '.zip';

  // Execution state
  executionStartTime: Date | null = null;
  executionCompleted: boolean = false;
  executionId!: string;

  public workflowLogs: any[] = [];
  public displayedLogsCount: number = 10;
  public showAllLogs: boolean = false;
  enableStreamingLog = environment.enableLogStreaming || 'all';

  public isExecutionComplete: boolean = false;
  progressInterval: any;

  // Component lifecycle
  private destroy$ = new Subject<void>();
  selectedTab: string = 'Agent Activity';
  demoTabs: AvaTab[] = [
    { id: 'activity', label: 'Agent Activity' },
    { id: 'agents', label: 'Agent Output' },
    { id: 'preview', label: 'Preview', disabled: true },
  ];
  errorMsg = false;
  resMessage: any;
  taskMessage: any[] = [];
  fullApiResponse: any = null;
  isJsonValid = false;
  disableChat : boolean = false;
  selectedFiles: File[] = [];
  workflowAgents: any[] = [];
  userInputList: any[] = [];
  progress = 0;
  isLoading = false;
  loaderColor: string = '';
  isWorkflowExecuting = false;

  inputFieldOrder: string[] = [];
  currentInputIndex: number = 0;
  activeTabId: string = 'nav-home';
  

  // New properties for workflow playground
  agents: AgentData[] = [];
  isPlaygroundCollapsed: boolean = false;
  pipelineAgents: any[] = [];
  fullWorkflowData: any = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private workflowService: WorkflowService,
    private inputExtractorService: WorkflowInputExtractorService,
    private tokenStorage: TokenStorageService,
    private loaderService: LoaderService,
    private formBuilder: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.loaderService.disableLoader();
    this.selectedTab = 'Agent Activity';
    this.executionId = crypto.randomUUID();
    // Get workflow ID from route params
    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      this.workflowId = params.get('id');
      if (this.workflowId) {
        this.loadWorkflow(this.workflowId);
      } else {
        // No workflow ID, redirect back to workflows page
        this.router.navigate(['/build/workflows']);
      }
    });
    // this.executeWorkflow()
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.loaderService.enableLoader();
  }
  onTabChange(event: { id: string, label: string }) {
    this.selectedTab = event.label;
    this.activeTabId = event.id;
    console.log('Tab changed to:', event);
    console.log('activeTabId is now:', this.activeTabId);
    console.log('fullApiResponse available:', !!this.fullApiResponse);
  }

  // Load workflow data
  loadWorkflow(id: string): void {
    // In a real app, this would fetch the workflow from a service
    console.log(`Loading workflow with ID: ${id}`);
    this.chatMessages = [
      {
        from: 'ai',
        text: 'I am your workflow assistant. I will help you in executing this workflow.',
      } as ChatMessage,
    ]
    this.workflowForm = this.formBuilder.group({});
    
    // Load workflow data for chat interface
    this.workflowService.getWorkflowById(id).subscribe({
        next: (res) => {
        this.workflowAgents = res.workflowAgents;
        this.userInputList = this.extractInputField(this.workflowAgents);
        if(this.userInputList.length === 0){
          this.disableChat = true;
        }
        this.workflowName = res.name;
        this.initializeForm();
        this.startInputCollection();
      },
        error: (err) => {
          this.disableChat = true;
          console.log(err);
        }
    });

    // Also load workflow details for the new playground component
    this.getWorkflowDetails(id);

  }

  // New method to get workflow details using workflow service
  public getWorkflowDetails(id: string) {
    this.workflowService.getOneWorkflow(id).subscribe({
      next: (res: any) => {
        // Store the full workflow data for configuration component
        this.fullWorkflowData = res;
        
        if (res?.pipeLineAgents?.length) {
          this.pipelineAgents = res.pipeLineAgents;
          this.workflowName = res.name;

          // Convert pipeline agents to AgentData format
          try {
            this.agents = this.inputExtractorService.convertToAgentData(this.pipelineAgents);
            console.log('Converted agents:', this.agents);
          } catch (error) {
            console.error('Error converting pipeline agents to AgentData:', error);
            this.agents = [];
          }

          // Extract input fields for form initialization
          this.userInputList = this.extractInputField(res.pipeLineAgents);
          this.initializeForm();
        } else {
          console.warn('No pipeline agents found in workflow response');
          this.agents = [];
        }
      },
      error: (e: any) => console.error(e)
    });
  }

  public extractInputField(pipeLineAgents: any) {
    const PLACEHOLDER_PATTERNS =  /(%\d+\$s)|\{\{([a-zA-Z0-9-_]+)\}\}/g;
    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};

    pipeLineAgents.forEach((pipelineAgent: any) => {
      const agentName = pipelineAgent?.agent?.name;
      const agentDescription = pipelineAgent?.agent?.task?.description;
      const matches = agentDescription?.matchAll(PLACEHOLDER_PATTERNS) || [];

      for (const match of matches) {
        const placeholder = match[1] || match[2];
        const placeholderInput = match[0];
        if (!placeholderMap[placeholder]) {
          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;
        }
        if (agentName) {
          placeholderMap[placeholder].agents.add(agentName);
        }
        placeholderMap[placeholder].inputs.add(placeholderInput);
      }
    })

    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({
      name: [...agents].length > 2
        ? `${[...agents].slice(0, -1).join(", ")} and ${[...agents].at(-1)}`
        : [...agents].join(" and "),
      placeholder,
      input: [...inputs][0],
    }));
  }

  public isImageInput(input: string): boolean {
    const match = input.match(/{{(.*?)}}/);
    if (match && match[1]) {
      const variableName = match[1].trim();
      return variableName.startsWith('image') || variableName.startsWith('Image');
    }
    return false;
  }

  public initializeForm() {
    console.log('Initializing form with userInputList:', this.userInputList);
    
    // Clear existing form controls to prevent duplicates
    Object.keys(this.workflowForm.controls).forEach(key => {
      this.workflowForm.removeControl(key);
    });
    
    // Use a Set to track added control names and prevent duplicates
    const addedControls = new Set<string>();
    
    // Add form controls from userInputList
    this.userInputList.forEach((label: any) => {
      const controlName = this.normalizeControlName(label.input);
      if (!addedControls.has(controlName)) {
        console.log('Adding form control for:', controlName);
        this.workflowForm.addControl(controlName, this.formBuilder.control('', Validators.required));
        addedControls.add(controlName);
      }
    });

    // Also add form controls for agent inputs to ensure compatibility
    if (this.agents) {
      this.agents.forEach(agent => {
        if (agent.inputs) {
          agent.inputs.forEach(input => {
            const controlName = this.normalizeControlName(input.placeholder);
            if (!addedControls.has(controlName)) {
              console.log('Adding additional form control for agent input:', controlName);
              this.workflowForm.addControl(controlName, this.formBuilder.control('', Validators.required));
              addedControls.add(controlName);
            }
          });
        }
      });
    }

    console.log('Final form controls:', Object.keys(this.workflowForm.controls));
  }

  public isInputValid() {
    return this.workflowForm.valid;
  }

  /**
   * Normalize control names to prevent duplicates with different formats
   * Removes curly braces and other special characters
   */
  private normalizeControlName(name: string): string {
    if (!name) return '';
    // Remove curly braces and trim whitespace
    return name.replace(/[{}]/g, '').trim();
  }

  startFakeProgress() {
    this.progress = 0;
    this.progressInterval = setInterval(() => {
      if (this.progress < 90) {
        this.progress += 5; // Increase slowly
      }
    }, 200); // Adjust speed
  }

  stopFakeProgress() {
    clearInterval(this.progressInterval);
    this.progress = 100;

    setTimeout(() => {
      this.isLoading = false;
    }, 500); // Small delay to let user see 100%
  }

  // Handle new chat message from user
  handleChatMessage(message: string): void {
    // console.log('message ', message, 'is blank', message.trim() === '');
    this.isProcessingChat = true;
    if(message.trim() === ''){
      if(this.inputFieldOrder.length === 0){
        this.executeWorkflow();
      }
      return;
    }

    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){
        this.executeWorkflow();
      return;
    }

    const field = this.inputFieldOrder[this.currentInputIndex];
    if (this.isImageInput(field)) {
      // Ignore text input, wait for file input
      return;
    }

    this.workflowForm.get(field)?.setValue(message);
    this.currentInputIndex++;

    if (this.currentInputIndex < this.inputFieldOrder.length) {
      this.promptForCurrentField();
    } else {
      this.executeWorkflow();
    }
  }

  // Save execution logs
  saveLogs(): void {
    console.log('Saving execution logs...');
    // This would typically save to a service
  }

  // Export results
  exportResults(section: 'activity' | 'output'): void {
    console.log(`Exporting ${section} data...`);

    if (section === 'activity') {
      const data = this.activityLogs
        .map((log) => `[${log.timestamp}] ${log.message}`)
        .join('\n');
      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');
    } else {
      const data = JSON.stringify(this.agentOutputs, null, 2);
      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');
    }
  }

  // Helper method to download data as a file
  private downloadAsFile(data: string, filename: string, type: string): void {
    const blob = new Blob([data], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  }

  // Handle controls for execution
  handleControlAction(action: 'play' | 'pause' | 'stop'): void {
    console.log(`Control action: ${action}`);
    // In a real app, this would control the workflow execution

    if (action === 'play') {
      this.isRunning = true;
    } else if (action === 'pause' || action === 'stop') {
      this.isRunning = false;
    }
  }

  // Navigate back to workflow listing
  navigateBack(): void {
    this.router.navigate(['/build/workflows']);
  }

  // Navigate to edit workflow
  editWorkflow(): void {
    if (this.workflowId) {
      this.router.navigate(['/build/workflows/edit', this.workflowId]);
    }
  }

  public logExecutionStatus(delay: number = 2000) {
    setTimeout(() => {
      if (!this.isExecutionComplete) {
        console.log(this.constants);
        console.log(this.constants['labels'].workflowExecProcessing);
        this.workflowLogs.push({
          content: this.constants['labels'].workflowExecProcessing,
          color: '#F9DB24',
        });
      }
    }, delay);
  }

  // public parseAnsiString(ansiString: string) {
  //   const regex = ansiRegex();
  //   const parts = ansiString.split(regex);
  //   const matches = [...ansiString.matchAll(regex)];
  //   parts.forEach((part, index) => {
  //     if (part.trim() !== '') {
  //       let colorCode = matches[index - 1][0];
  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\u001b[1m')) {
  //         colorCode = `\u001b[1m${colorCode}`;
  //       }
  //       this.workflowLogs.push({
  //         content: part,
  //         color: this.colorMap[colorCode] || 'white',
  //       });
  //     }
  //   });
  // }

  public getWorkflowLogs(executionId: string) {
    console.log('Attempting to connect to WebSocket for executionId:', executionId);

    try {
      this.workflowService
        .workflowLogConnect(executionId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (message) => {
            console.log('WebSocket message received:', message);
            const { content, color } = message;
            if (color) {
              this.workflowLogs.push({ content, color });
            } else if (this.enableStreamingLog === 'all') {
              // this.parseAnsiString(content);
              this.workflowLogs.push({ content, color: '#6B7280' });
            }
          },
          error: (err) => {
            console.error('WebSocket connection error:', err);
            this.workflowLogs.push({
              content: 'WebSocket connection failed. Using demo logs instead.',
              color: 'red',
            });
          },
          complete: () => {
            this.logExecutionStatus();
            console.log('WebSocket connection closed');
          },
        });
    } catch (error) {
      console.error('Failed to establish WebSocket connection:', error);
      this.workflowLogs.push({
        content: 'Failed to connect to log streaming service. Using demo logs.',
        color: 'red',
      });
    }
  }

  // public parseAnsiString(ansiString: string) {
  //   const regex = ansiRegex();
  //   const parts = ansiString.split(regex);
  //   const matches = [...ansiString.matchAll(regex)];
  //   parts.forEach((part, index) => {
  //     if (part.trim() !== '') {
  //       let colorCode = matches[index-1][0];
  //       if(index - 2 >= 0 && matches[index-2]?.includes('\u001b[1m')) {
  //         colorCode = `\u001b[1m${colorCode}`;
  //       }
  //       this.workflowLogs.push({
  //         content: part, 
  //         color: this.colorMap[colorCode] || 'white', 
  //       });
  //     }
  //   });
  // }

  public validateJson(output: string): any | null {
    this.isJsonValid = false;
    try {
      const parsedOutput = JSON.parse(output);
      this.isJsonValid = true;
      return parsedOutput;
    } catch (e) {
      return null;
    }
  }

  public executeWorkflow() {
    let payload: FormData | Record<string, any> = new FormData();
    let queryString = '';

    console.log('Executing workflow with form value:', this.workflowForm.value);
    console.log('Form controls:', Object.keys(this.workflowForm.controls));
    console.log('Agent inputs:', this.agents.map(a => ({ name: a.name, inputs: a.inputs })));

    // Set loading state
    this.isWorkflowExecuting = true;
    this.status = ExecutionStatus.running;
    if (this.selectedFiles.length) {
      this.selectedFiles.forEach((file) => {
        payload.append('files', file);
      });
      payload.append('workflowId', this.workflowId);
      payload.append('userInputs', JSON.stringify(this.workflowForm.value));
      payload.append('user', this.tokenStorage.getDaUsername());
      payload.append('executionId', this.executionId);
      queryString = '/files';
    } else {
      payload = {
        pipeLineId: this.workflowId,
        userInputs: this.workflowForm.value,
        executionId: this.executionId,
        user: this.tokenStorage.getDaUsername(),
      };
    }

    console.log('Final payload:', payload);

    this.getWorkflowLogs(this.executionId);
    this.startFakeProgress();

    this.workflowService
      .executeWorkflow(payload, queryString)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.isProcessingChat = false;
          this.isRunning = false;

          if (res?.workflowResponse?.pipeline?.output) {
            this.isExecutionComplete = true;
            // console.log(this.constants['labels'].workflowExecComplete);
            this.workflowLogs.push({
              content: this.constants['labels'].workflowExecComplete,
              color: '#0F8251',
            });
            this.errorMsg = false;
            this.resMessage = res?.workflowResponse?.pipeline?.output;

            // Store the complete API response for display
            this.fullApiResponse = res;

            // Map all task outputs to the taskMessage array
            this.taskMessage = (res?.workflowResponse?.pipeline?.tasksOutputs || []).map((task: any, index: number) => ({
              id: `task-output-${index}`,
              title: task?.description || `Task ${index + 1} Output`,
              content: task?.raw || JSON.stringify(task, null, 2),
              agentName: task?.agent_name || 'Task',
              timestamp: new Date().toISOString(),
              type: 'json',
              description: task?.description || '',
              expected_output: task?.expected_output || '',
              summary: task?.summary || '',
              raw: task?.raw || ''
            }));

            // If no task outputs but we have a response, add a default output
            if (this.taskMessage.length === 0 && res) {
              this.taskMessage = [{
                id: 'workflow-output',
                title: 'Workflow Output',
                content: JSON.stringify(res, null, 2),
                agentName: 'Workflow',
                timestamp: new Date().toISOString(),
                type: 'json',
                description: 'Workflow execution result',
                expected_output: '',
                summary: '',
                raw: JSON.stringify(res)
              }];
            }

            console.log('Full workflow response stored:', this.fullApiResponse);
            console.log('Mapped taskMessage:', this.taskMessage);

            // Automatically switch to Output tab after execution completes
            this.activeTabId = 'nav-products';
            console.log('Switched to Output tab, activeTabId:', this.activeTabId);

            // if("file_download_url" in res?.pipeline){
            //   this.isFileWriter = true;
            //   this.fileDownloadLink = res?.pipeline?.file_download_url;

            //   if(!this.fileDownloadLink){
            //     this.fileDownloadUrlError = [];
            //     this.fileDownloadUrlError.push("Output file is not generated yet!")
            //   }
            // }
            // this.isAccordian = true
          }
          this.validateJson(this.resMessage);
          this.status = ExecutionStatus.completed;
          this.stopFakeProgress();
          this.selectedFiles = [];
          // Clear loading state
          this.isWorkflowExecuting = false;
        },
        error: (error) => {
          this.isExecutionComplete = true;
          this.isProcessingChat = false;
          this.errorMsg = true;
          this.resMessage = error?.error?.detail;
          this.workflowService.workflowLogDisconnect();
          this.workflowLogs.push({
            content: this.constants['labels'].workflowLogFailed,
            color: 'red',
          });
          this.selectedFiles = [];
          this.stopFakeProgress();
          // Clear loading state on error
          this.isWorkflowExecuting = false;
          console.log('error is', error.message);
        },
      });
  }

  // public asyncExecutePipeline() {
  //   const payload: FormData = new FormData();
  //   if (this.selectedFiles?.length) {
  //     for (const element of this.selectedFiles) {
  //       payload.append('files', element)
  //     }
  //   }
  //   payload.append('pipeLineId', String(this.workflowId));
  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));
  //   payload.append('user', this.tokenStorage.getDaUsername() || '');
  //   payload.append('executionId', this.executionId);

  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({
  //     next: (res: any) => {
  //       if(res) {
  //         // res handling
  //         console.log(res);
  //       }
  //     },
  //     error: e => {
  //       // error handling
  //       console.log(e);
  //     }
  //   })
  // }

  handleAttachment() {
    console.log('handleAttachment');
  }

  onAttachmentsSelected(files: File[]) {
    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){
      this.selectedFiles = files;
      return;
    }

    const field = this.inputFieldOrder[this.currentInputIndex];
    if(this.isImageInput(field)){
      if (files && files.length > 0) {
        this.onImageSelected(files[0]);
      }
    } else {
      this.selectedFiles = files;
    }
  }

  startInputCollection(){
    this.inputFieldOrder = Object.keys(this.workflowForm.controls);
    this.currentInputIndex = 0;
    if (this.inputFieldOrder.length > 0) {
      this.promptForCurrentField();
    }
    else{
      this.disableChat = true;
    }
  }

  promptForCurrentField() {
    const field = this.inputFieldOrder[this.currentInputIndex];
    if (this.isImageInput(field)) {
      this.fileType = '.jpeg,.png,.jpg,.svg';
      // UI should now show a file input for the user
    } else {
      this.fileType = '.zip'; // or whatever default you want for non-image
    }
  }

  onImageSelected(file: File) {
    const field = this.inputFieldOrder[this.currentInputIndex];
    if (!this.isImageInput(field)) return;

    const reader = new FileReader();
    reader.onload = () => {
      const base64String = (reader.result as string); 
      this.workflowForm.get(field)?.setValue(base64String);
      this.currentInputIndex++;
      if (this.currentInputIndex < this.inputFieldOrder.length) {
        this.promptForCurrentField();
      } else {
        this.executeWorkflow();
      }
    };
    reader.readAsDataURL(file);
  }

  // Event handlers for workflow playground
  onPlaygroundBackClicked(): void {
    this.navigateBack();
  }

  onPlaygroundCollapseToggled(isCollapsed: boolean): void {
    this.isPlaygroundCollapsed = isCollapsed;
  }

  onAgentInputChanged(event: {agentId: number, inputIndex: number, value: string}): void {
    // Find the agent and update the input value
    const agent = this.agents.find(a => a.id === event.agentId);
    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {
      agent.inputs[event.inputIndex].value = event.value;

      // Update the form control if it exists
      const placeholder = agent.inputs[event.inputIndex].placeholder;
      if (this.workflowForm.get(placeholder)) {
        this.workflowForm.get(placeholder)?.setValue(event.value);
      }
    }
  }

  onAgentFileSelected(event: {agentId: number, inputIndex: number, files: File[]}): void {
    // Find the agent and update the files
    const agent = this.agents.find(a => a.id === event.agentId);
    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {
      agent.inputs[event.inputIndex].files = event.files;

      // Add files to selectedFiles array for execution
      this.selectedFiles = [...this.selectedFiles, ...event.files];
    }
  }

  onMessageSent(event: {agentId: number, inputIndex: number, value: string, files?: File[]}): void {
    console.log('Message sent from agent:', event);

    // Find the agent and update the input value
    const agent = this.agents.find(a => a.id === event.agentId);
    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {
      agent.inputs[event.inputIndex].value = event.value;

      // Update files if provided
      if (event.files) {
        agent.inputs[event.inputIndex].files = event.files;
        this.selectedFiles = [...this.selectedFiles, ...event.files];
      }

      // Update the form control if it exists
      const normalizedPlaceholder = this.normalizeControlName(agent.inputs[event.inputIndex].placeholder);
      console.log('Updating form field:', normalizedPlaceholder, 'with value:', event.value);
      console.log('Form controls available:', Object.keys(this.workflowForm.controls));
      console.log('Current form value before update:', this.workflowForm.value);

      if (this.workflowForm.get(normalizedPlaceholder)) {
        this.workflowForm.get(normalizedPlaceholder)?.setValue(event.value);
        console.log('Form value after update:', this.workflowForm.value);
      } else {
        console.warn('Form control not found for placeholder:', normalizedPlaceholder);
        // Try to add the control dynamically with normalized name
        this.workflowForm.addControl(normalizedPlaceholder, this.formBuilder.control(event.value));
        console.log('Added new form control. Form value:', this.workflowForm.value);
      }

      // Log the input submission
      this.workflowLogs.push({
        content: `Agent "${agent.name}" - Input "${agent.inputs[event.inputIndex].inputName}": ${event.value}`,
        color: '#10B981'
      });
    }
  }

  /**
   * Filter out input-related logs from the workflow logs
   */
  get filteredLogs(): any[] {
    // Filter out logs that contain input-related messages
    return this.workflowLogs.filter(log => 
      !log.content?.toLowerCase().includes('input') && 
      !log.content?.toLowerCase().includes('agent') || 
      (log.content?.toLowerCase().includes('error') || log.content?.toLowerCase().includes('failed'))
    );
  }

  /**
   * Get the logs to display based on the current view state
   */
  get displayedLogs(): any[] {
    const logs = this.filteredLogs;
    if (this.showAllLogs) {
      return logs;
    }
    return logs.slice(0, this.displayedLogsCount);
  }

  /**
   * Toggle between showing limited logs and all logs
   */
  toggleShowAllLogs(): void {
    this.showAllLogs = !this.showAllLogs;
  }

  /**
   * Check if there are more logs to show
   */
  get hasMoreLogs(): boolean {
    return this.filteredLogs.length > this.displayedLogsCount && !this.showAllLogs;
  }
}
