<div id="workflows-container" class="container-fluid">
  <div id="search-filter-container" class="row g-3">
    <div class="col-12 col-md-12 col-lg-12 col-xl-12 search-section">
      <form [formGroup]="searchForm">
        <ava-textbox
          placeholder='Search "Workflows"'
          hoverEffect="glow"
          pressedEffect="solid"
          formControlName="search"
        >
          <ava-icon
            slot="icon-start"
            iconName="search"
            [iconSize]="16"
            iconColor="var(--color-brand-primary)"
          >
          </ava-icon>
        </ava-textbox>
      </form>
    </div>
    <!--<div class="col-12 col-md-4 col-lg-3 col-xl-2 action-buttons">
      <ava-dropdown
        dropdownTitle="choose workflow"
        [options]="workflowsOptions"
        (selectionChange)="onSelectionChange($event)"
      >
      </ava-dropdown>
    </div>-->
  </div>

  <div id="prompts-card-container" class="row g-3">
    <ava-text-card
      class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      title="Create Workflow"
      (cardClick)="onCreateWorkflow()"
      [isLoading]="isLoading"
    >
    </ava-text-card>

    <!-- No Results Message -->
    <div
      class="col-12 d-flex justify-content-center align-items-center py-5"
      *ngIf="!isLoading && displayedWorkflows.length === 0"
    >
      <div class="text-center">
        <h5 class="text-muted">No workflow found matching your criteria</h5>
      </div>
    </div>

    <ng-container
      *ngFor="
        let workflow of isLoading && displayedWorkflows.length === 0
          ? cardSkeletonPlaceholders
          : displayedWorkflows
      "
    >
      <ava-console-card
        class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
        [title]="workflow?.title"
        [description]="workflow?.description"
        categoryIcon="bot"
        categoryTitle="Workflows"
        categoryValue="42"
        [author]="workflow?.owner || 'AAVA'"
        [date]="workflow?.createdDate | timeAgo"
        [actions]="defaultActions"
        (actionClick)="onActionClick($event, workflow.id)"
        [skeleton]="isLoading"
      >
      </ava-console-card>
    </ng-container>
  </div>

  <!-- Pagination Footer -->
  <div class="row" *ngIf="filteredWorkflows.length > 0">
    <div class="col-12 d-flex justify-content-center mt-4">
      <app-page-footer
        [totalItems]="totalRecords"
        [currentPage]="currentPage"
        [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>
    </div>
  </div>
</div>
