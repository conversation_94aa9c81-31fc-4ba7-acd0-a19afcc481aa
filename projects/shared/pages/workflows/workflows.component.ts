import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule, DatePipe } from '@angular/common';
import { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';
import { PaginationService } from '@shared/services/pagination.service';
import { WorkflowService } from '@shared/services/workflow.service';
import { TokenStorageService } from '@shared/index';

import {
  ButtonComponent,
  IconComponent,
  DropdownComponent,
  DropdownOption,
  AvaTextboxComponent,
  DialogService,
  TextCardComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import {
  ConsoleCardComponent,
  ConsoleCardAction,
} from '@shared/components/console-card/console-card.component';
import { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged, map } from 'rxjs';
import { WorkflowModes } from './constants/workflow.constants';
import workflowConstants from './constants/workflows.json';
import { DebouncedSearchService } from '../../services/debounced-search.service';

@Component({
  selector: 'app-workflows',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    ReactiveFormsModule,
    ConsoleCardComponent,
    TimeAgoPipe
  ],
  providers: [DatePipe, DialogService],
  templateUrl: './workflows.component.html',
  styleUrl: './workflows.component.scss',
})
export class WorkflowsComponent implements OnInit, OnDestroy {
  defaultActions: ConsoleCardAction[] = [
    {
      id: 'duplicate',
      icon: 'copy',
      label: 'Duplicate',
      tooltip: 'Duplicate',
    },
    {
      id: 'edit',
      icon: 'edit',
      label: 'Edit item',
      tooltip: 'Edit',
    },
    {
      id: 'delete',
      icon: 'trash',
      label: 'Delete item',
      tooltip: 'Delete',
    },
    {
      id: 'run',
      icon: 'play',
      label: 'Run application',
      tooltip: 'Run',
      isPrimary: true,
    },
  ];
  allWorkflows: any[] = [];
  filteredWorkflows: any[] = [];
  displayedWorkflows: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 11;
  totalPages: number = 1;
  workflowsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
  searchForm!: FormGroup;
  cardSkeletonPlaceholders = Array(11);
  public totalRecords: number = 25;
  public showDeleteWorkflowPopup: boolean = false;
  public showInfoPopup: boolean = false;
  public infoMessage: string = '';
  public showErrorPopup: boolean = false;
  public labels = workflowConstants.labels;
  public workflowId: string = '';
  isSearching: boolean = false;

  constructor(
    private paginationService: PaginationService,
    private debounceService: DebouncedSearchService,
    private router: Router,
    private workflowService: WorkflowService,
    private datePipe: DatePipe,
    private fb: FormBuilder,
    private tokenStorage: TokenStorageService,
    private dialogService: DialogService

  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.fetchAllWorkflows();
  }

  private searchList() {
    this.searchForm
      .get('search')!
      .valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe((searchText: string) => {
        const trimmed = searchText.trim();
        if (trimmed) {
          // API-based search
          this.isLoading = true;
          this.debounceService.triggerSearch(trimmed, 'workflows', 'default');
        } else {
          // Fallback to default list
          this.fetchAllWorkflows();
        }
      });
    this.debounceService.searchResults$.subscribe({
      next: (results: any) => {
        this.isLoading = false;

        // Extract `workflowDetails` safely
        const workflows = Array.isArray(results?.workflowDetails)
          ? results.workflowDetails
          : [];

        this.allWorkflows = workflows.map((item: any) => ({
          ...item,
          id: item.id,
          title: item.name,
          name: item.name,
          description: item.description || 'No description',
          createdDate: this.datePipe.transform(item.createdAt, 'MM/dd/yyyy') || '',
        }));

        this.filteredWorkflows = [...this.allWorkflows];
        this.isSearching = true;
        this.totalPages = Math.ceil(this.filteredWorkflows.length / this.itemsPerPage);
        this.totalRecords = results.totalNoOfRecords;
        this.updateDisplayedWorkflows(); // Ensure visible in UI
      },
      error: () => {
        this.error = 'Search failed.';
        this.isLoading = false;
      },
    });
  }

  private fetchAllWorkflows() {
    this.isLoading = true;
    this.workflowService
      .fetchAllV2Workflows(this.currentPage, this.itemsPerPage, false)
      .subscribe({
        next: (response: any) => {
          this.totalRecords = response.totalNoOfRecords;
          const pipeLines = response.workflowDetails || response;
          this.allWorkflows = pipeLines.map((item: any) => ({
            ...item,
            id: item.id,
            title: item.name,
            name: item.name,
            description: item.description || 'No description',
            createdDate:
              this.datePipe.transform(item.createdAt, 'MM/dd/yyyy') || '',
          }));
          this.filteredWorkflows = [...this.allWorkflows];
          this.displayedWorkflows = [...this.allWorkflows];
          this.isLoading = false;
        },
        error: (error) => {
          this.error = error.message || 'Failed to load workflows';
          this.isLoading = false;
        },
      });
  }


  private updateDisplayedWorkflows(): void {

    if (this.isSearching) {

      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;

      this.displayedWorkflows = this.filteredWorkflows.slice(startIndex, endIndex);
      this.totalPages = Math.ceil(this.filteredWorkflows.length / this.itemsPerPage);
    }
    else {
      if (this.currentPage === 1) {
        this.itemsPerPage = 11;
      } else {
        this.itemsPerPage = 12;
      }
      const paginationResult = this.paginationService.getPaginatedItems(
        this.filteredWorkflows,
        this.currentPage,
        this.itemsPerPage
      )
      this.displayedWorkflows = paginationResult.displayedItems;
      this.totalPages = paginationResult.totalPages;

    }

  }

  public onCreateWorkflow(): void {
    this.router.navigate(['/build/workflows/create']);
  }

  private editWorkflow(workflowId: string): void {
    this.router.navigate(['/build/workflows/edit', workflowId]);
  }

  public onActionClick(
    event: { actionId: string; action: ConsoleCardAction },
    workflowId: string,
  ): void {
    switch (event.actionId) {
      case 'edit':
        this.editWorkflow(workflowId);
        break;
      case 'delete':
        this.handleDeleteWorkflow(workflowId);
        break;
      case 'duplicate':
        this.duplicateWorkflow(workflowId);
        break;
      case 'run':
        this.executePrompt(workflowId);
        break;
      default:
        break;
    }
  }

  executePrompt(workflowId: string): void {
    this.router.navigate(['/build/workflows/execute', workflowId]);
  }

  public handleDeleteWorkflow(workflowId: string): void {
    const workflow = this.filteredWorkflows.find(w => w.id === workflowId);
    if (!workflow) return;

    this.dialogService.confirmation({
      title: 'Delete Workflow',
      message: `Are you sure you want to delete "${workflow.name}"? This action cannot be undone.`,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
    }).then(result => {
      if (result.confirmed) {
        this.deleteWorkflow(workflowId);
      }
    });
  }

  private deleteWorkflow(workflowId: string): void {
    // Store the loading dialog reference
    const loadingDialog = this.dialogService.loading({
      title: 'Deleting Workflow',
      message: 'Please wait while we delete the workflow...',
      showProgress: true
    });

    const modifiedBy = this.tokenStorage.getDaUsername() || '';
    this.workflowService.deleteWorkflow(workflowId, modifiedBy).subscribe({
      next: (response: any) => {
        // Close the loading dialog
        this.dialogService.close();
        
        // Show success message
        this.dialogService.success({
          title: 'Workflow Deleted',
          message: response?.message || 'Workflow deleted successfully',
        }).then(() => {
          this.fetchAllWorkflows();
        });
      },
      error: (error: any) => {
        // Close the loading dialog
        this.dialogService.close();
        
        // Show error message
        this.dialogService.error({
          title: 'Error',
          message: error?.message || 'Failed to delete workflow',
          retryButtonText: 'OK'
        });
      },
    });
  }



  private duplicateWorkflow(workflowId: string): void {
    this.router.navigate(['/build/workflows/create'], {
      queryParams: {
        id: workflowId,
        mode: WorkflowModes.duplicate,
      },
    });
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
  }

  public onPageChange(page: number): void {
    this.currentPage = page;

    if (this.isSearching) {
      // If in search mode, just paginate filtered results
      this.updateDisplayedWorkflows();
    } else {
      // Normal fetch from server
      this.fetchAllWorkflows();
    }
  }

  ngOnDestroy(): void {
    this.searchForm.get('search')?.setValue('', { emitEvent: false });
    this.filteredWorkflows = [];
    this.allWorkflows = [];
  }
}