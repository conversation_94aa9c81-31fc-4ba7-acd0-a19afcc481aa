<div class="workflow-editor-container">
  <!-- Main Content Area -->
  <div class="main-content">
    <!-- Full Width Canvas Area -->
    <div class="canvas-area">
      <!-- Agent Library Floating Panel -->
      <div
        class="agent-library-panel"
        [class.collapsed]="isPanelCollapsed"
        [ngClass]="{ 'p-3': !isPanelCollapsed }"
      >
        <div class="panel-header" (click)="togglePanel()">
          <h3>Agent Library</h3>
          <ava-icon
            [iconName]="isPanelCollapsed ? 'ChevronDown' : 'ChevronUp'"
            iconSize="16"
            iconColor="var(--text-secondary)"
          >
          </ava-icon>
        </div>

        <!-- Panel Content -->
        <div class="panel-content" [class.hidden]="isPanelCollapsed">
          <!-- Search Section -->
          <div class="search-section">
            <form [formGroup]="searchForm">
              <ava-textbox
                placeholder='Search "Agents"'
                hoverEffect="glow"
                pressedEffect="solid"
                formControlName="agentFilter"
              >
                <ava-icon
                  slot="icon-start"
                  iconName="search"
                  [iconSize]="16"
                  iconColor="var(--color-brand-primary)"
                >
                </ava-icon>
              </ava-textbox>
            </form>
          </div>

          <!-- Agent List -->
          <div class="agents-list">
            <!-- Agent Items -->
            <div
              *ngFor="let agent of filteredAgents"
              class="agent-item"
              draggable="true"
              (dragstart)="onDragStart($event, agent)"
            >
              <div class="agent-header">
                <div class="agent-icon-box">
                  <ava-icon
                    iconName="User"
                    iconSize="20"
                    iconColor="#ffffff"
                  ></ava-icon>
                </div>
                <h4 class="agent-name">{{ agent.name }}</h4>
                <div class="agent-count">
                  <ava-icon
                    iconName="Users"
                    iconSize="16"
                    iconColor="#9CA3AF"
                  ></ava-icon>
                  <span class="count-text">120</span>
                </div>
              </div>
              <p class="agent-description">{{ agent.description }}</p>

              <!-- Agent Tags -->
              <div
                class="agent-tags"
                *ngIf="agent.capabilities && agent.capabilities.length > 0"
              >
                <span
                  class="agent-tag"
                  *ngFor="let capability of agent.capabilities"
                  >{{ capability }}</span
                >
              </div>

              <!-- Preview button -->
              <div class="agent-actions">
                <ava-button
                  label="Preview"
                  size="small"
                  [pill]="true"
                  variant="secondary"
                  (userClick)="onItemPreview(agent)"
                  class="preview-btn"
                >
                </ava-button>
              </div>
            </div>

            <!-- No results message -->
            <div
              class="no-results-message"
              *ngIf="availableAgents.length === 0"
            >
              <div class="no-results-content">
                <ava-icon
                  iconName="Search"
                  iconSize="24"
                  iconColor="#9CA3AF"
                ></ava-icon>
                <p>No agents found matching your criteria</p>
              </div>
            </div>
          </div>

          <!-- Create New Agent Button -->
          <div class="create-agent-section">
            <ava-button
              label="Create New Agent"
              variant="primary"
              size="large"
              [customStyles]="{
                background:
                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                '--button-effect-color': '33, 90, 214',
                'border-radius': '8px',
              }"
              iconName="Plus"
              iconColor="white"
              (userClick)="onCreateNewAgent()"
              [width]="'100%'"
            >
            </ava-button>
          </div>
        </div>
      </div>

      <!-- Workflow Editor Canvas -->
      <div class="editor-canvas">
        <app-canvas-board
          [nodes]="canvasNodes"
          [edges]="canvasEdges"
          [fallbackMessage]="workFlowLabels.fallbackMessage"
          [primaryButtonText]="workFlowLabels.execute"
          (canvasDropped)="onCanvasDropped($event)"
          (nodeSelected)="onNodeSelected($event)"
          (nodeMoved)="onNodeMoved($event)"
          (nodeRemoved)="onDeleteNode($event)"
          (connectionStarted)="onStartConnection($event)"
          (connectionCreated)="onConnectionCreated($event)"
          (undoAction)="onUndo()"
          (redoAction)="onRedo()"
          (resetAction)="onReset()"
          (primaryButtonClicked)="onExecute()"
          (stateChanged)="onCanvasStateChanged($event)"
          [showLeftActions]="true"
          [showHeaderInputs]="true"
          [inputFieldsConfig]="inputFieldsConfig"
          [agentDetailNameControl]="getControl('name')"
          [agentDetailControl]="getControl('description')"
        >
          <!-- Node template for rendering agent nodes -->
          <ng-template
            #nodeTemplate
            let-node
            let-selected="selected"
            let-onDelete="onDelete"
            let-onMove="onMove"
            let-onSelect="onSelect"
            let-onStartConnection="onStartConnection"
          >
            <app-agent-node
              [node]="node"
              [selected]="selected"
              (deleteNode)="onDelete($event)"
              (moveNode)="onMove($event)"
              (nodeSelected)="onSelect($event)"
              (startConnection)="onStartConnection($event)"
              (nodePositionChanged)="updateNodePosition($event)"
              (toolValuesChanged)="builtInToolValuesChanged($event)"
              [patchData]="getToolPatchValueForAgent(node?.data?.agentId)"
            >
            </app-agent-node>
          </ng-template>

          <div
            info
            class="model-info"
            *ngIf="
              getControl('enableManagerLLM').value &&
              getControl('modelDeploymentName')?.value as modelValue
            "
          >
            You are currently using {{ modelValue }}
          </div>

          <ng-container actionLeft>
            <div class="sidebar-section llm-toggle full-width">
              <div class="llm-toggle-container">
                <div class="toggle-container">
                  <span class="toggle-label">{{
                    workFlowLabels.managerLLMToggleLabel
                    }}</span>
                  <ava-toggle size="small" (checkedChange)="onToggleChnage($event)" position="left" [animation]="true"
                    [checked]="!!getControl('enableManagerLLM').value">
                  </ava-toggle>
                </div>
              </div>
              <div class="llm-settings" *ngIf="getControl('enableManagerLLM').value && showLlm" #llmSettingsRef>
                <!-- Model List -->
                <div class="setting-item">
                  <h3>{{workFlowLabels.model}}</h3>
                  <ava-dropdown dropdownTitle="choose agent" [options]="modelList"
                    [formControl]="getControl('modelDeploymentName')">
                  </ava-dropdown>
                </div>
                <!-- Temperature -->
                <div class="setting-item">
                  <h3>{{workFlowLabels.temperature}}</h3>
                  <ava-slider [min]="0" [max]="1" [step]="0.1" [formControl]="getControl('temperature')"></ava-slider>
                </div>
          
                <!-- Top P -->
                <div class="setting-item">
                  <h3>{{workFlowLabels.topP}}</h3>
                  <input type="number" min="0" max="1" step="0.01" [formControl]="getControl('topP')" class="setting-input" />
                </div>
          
                <!-- Max RPM -->
                <div class="setting-item">
                  <h3>{{workFlowLabels.maxRPM}}</h3>
                  <input type="number" min="0" [formControl]="getControl('maxRPM')" class="setting-input" />
                </div>
          
                <!-- Max Token -->
                <div class="setting-item">
                  <h3>{{workFlowLabels.maxToken}}</h3>
                  <div class="token-container">
                    <input type="number" min="0" [formControl]="getControl('maxToken')" class="setting-input" />
                    <span class="tokens-used">
                      {{ getControl("maxToken").value }}/{{workFlowLabels.tokensUsed}}
                    </span>
                  </div>
                </div>
          
                <!-- Max Iteration -->
                <div class="setting-item">
                  <h3>{{workFlowLabels.maxIteration}}</h3>
                  <input type="number" min="1" [formControl]="getControl('maxIteration')" class="setting-input" />
                </div>
          
                <!-- Max Execution Time -->
                <div class="setting-item">
                  <h3>{{workFlowLabels.maxExecutionTime}}</h3>
                  <input type="number" min="0" [formControl]="getControl('maxExecutionTime')" class="setting-input" />
                </div>
              </div>
            </div>
          </ng-container>
        </app-canvas-board>
      </div>
    </div>
  </div>
</div>

<!-- Add this container somewhere in your template -->
<div #drawerContainer></div>
