.create-knowledge-base-container {
  width: 100%;
  height: 90%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

.knowledge-base-head{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.knowledge-base-title {
  font-size: 1.5rem;         /* Bigger font */
  font-weight: bold;         /* Make it bold */
  color: #333333;            /* Dark color for readability */
  padding-bottom: 4px;
}

.loading-overlay {
  // position: absolute;
  // top: 0;
  // left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 0px;
  //padding: 20px;
  flex: 1;
  overflow: hidden;
  align-items: stretch;
  height: 100%;
  border: 1px solid #e1e4e8;
  background: #ffffff;
  // margin-bottom: 20px;

  @media (max-width: 1400px) {
    gap: 20px;
  }

  @media (max-width: 1200px) {
    gap: 16px;
    padding: 16px;
  }

  @media (max-width: 992px) {
    flex-direction: column;
  }

  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
  }

  .left-column,
  .right-column {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 20px;
    @media (max-width: 1200px) {
      gap: 16px;
      height: calc(100vh - 160px);
    }

    @media (max-width: 992px) {
      width: 100%;
      height: auto;
      max-height: none;
    }

    @media (max-width: 576px) {
      gap: 12px;
    }
  }

  .left-column {
    width: 340px;
    transition: width 0.3s cubic-bezier(0.4,0,0.2,1);
    background: #f8f9fa;
    border-right: 1px solid #e1e4e8;
    display: flex;
    flex-direction: column;
    height: 100vh;
    min-height: 0;
    position: relative;

    @media (max-width: 1400px) {
      width: 40%;
    }

    @media (max-width: 1200px) {
      width: 40%;
    }

    app-card {
      flex-shrink: 0;
    }

    app-card:first-of-type {
      flex: 0 0 auto;
    }

    app-card:last-of-type {
      flex: 1;
    }
  }

  .right-column {
    width: 100%;
    //flex-shrink: 0;
    display: flex;
    flex-direction: column;

    @media (max-width: 1400px) {
      width: 60%;
    }

    @media (max-width: 1200px) {
      width: 60%;
    }
  }
}

.card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;

  @media (max-width: 576px) {
    padding: 12px;
    gap: 12px;
  }
}

.left-column {
  .card-content {
    flex: 1 1 0;
   min-height: 0;
   overflow-y: auto;
   padding: 1rem;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);

  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
}

// Section styling
.section {
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.split-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.retriever-options {
  display: flex;
  gap: 8px;
}

.retriever-button {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  background-color: white;
  border: 1px solid #e1e4e8;
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;

  &.selected {
    background-color: black;
    color: white;
    border-color: black;
  }

  &:hover:not(.selected) {
    background-color: #f0f0f0;
  }
}


// Split size slider styling
.split-size-container {
  float: left;
  width: 600px;
}

.split-size-slider {
  flex: 1;
  height: 4px;
  appearance: none;
  background-color: var(--agent-slider-bg);
  border-radius: 2px;
  outline: none;

  &::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background-color: var(--agent-slider-thumb-bg);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--agent-slider-thumb-shadow);
  }

  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background-color: var(--agent-slider-thumb-bg);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: var(--agent-slider-thumb-shadow);
  }
}

.split-size-input {
  width: 50px;
  padding: 6px;
  border: 1px solid var(--agent-slider-input-border);
  border-radius: 6px;
  text-align: center;
  background-color: var(--agent-slider-input-bg);
  color: var(--agent-slider-input-text);
}


.split-size-container {
  display: flex;
  align-items: center;
  gap: 16px; // spacing between slider and input
  width: 100%;
  padding-bottom: 10px;

  ava-slider {
    flex: 1; // allow slider to take remaining space
    min-width: 200px;
  }

  .split-size-input {
    width: 100px;
    padding: 6px 8px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 6px;
  }
}


.field-error {
  color: #e53935; // Bright red for errors
  font-size: 13px;
  margin-top: 4px;
}


// Bottom buttons
.right-column-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 0px 0 4px;
  // margin-top: 10px;
  margin-bottom: 0;
  flex-shrink: 0;

  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px 0 0;
    margin-top: 8px;
    margin-bottom: 0;
  }

  .exit-button,
  .save-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
    }
  }

  .exit-button {
    background-color: transparent;
    border: 1px solid var(--button-secondary-border);
    color: var(--button-secondary-text);

    &:hover {
      background-color: var(--button-secondary-hover-bg);
    }
  }

  .save-button {
    background: var(--button-gradient);
    border: none;
    color: var(--button-primary-text);

    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }
}


.upload-fields-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); // 2 equal columns
  gap: 16px; // Adjust spacing as needed

  h3 {
    grid-column: 1 / -1; // Span across both columns
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.value-box {
  width: 70px;
  height: 49px;
  border: 1px solid #4B507F;
  border-radius: 8px; // Rounded corners
  font-size: 16px; // Text styling
  font-family: Inter, sans-serif;
  text-align: center;
  line-height: 30px; // Vertically center text
  color: #4B507F; // Match stroke color
  background-color: white;
  padding: 0;
}

::ng-deep ava-file-upload {
  .upload-container {

    .file-actions,
    .ava-icon-container {
      display: none;
    }
  }
}

.ava-file-upload-left {
  width: 60%;
  float: left;
}

.ava-file-upload-wrapper {
  width: 100%;
}

.page-title {
    font-weight: 600;
    font-size: 20px;
}

.schema-dropdown{
  margin-top: 10px;
}

.dropdown-medium {
  width: 250px;
  display: inline-block;
}

.left-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e1e4e8;
  z-index: 2;
}

.collapse-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  cursor: pointer;
  z-index: 2;
  font-size: 18px;
  border: 1px solid #e1e4e8;
  margin-right: 0;
}

.left-title {
  font-weight: 600;
  font-size: 16px;
  color: #23272E;
  //margin-left: 12px;
}

.left-column.collapsed {
  width: 48px !important;
  min-width: 48px !important;
  max-width: 48px !important;
}
.left-column.collapsed .card-content {
  display: none;
}
.right-column {
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  overflow-x: hidden;

}

.right-column::-webkit-scrollbar {
  display: none;
}
::ng-deep ava-dropdown label {
  margin-bottom: 10px; /* or any space you want */
  display: inline-block;
}