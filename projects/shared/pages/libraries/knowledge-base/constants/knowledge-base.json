{"labels": {"pageTitle": "Knowledge Base Configuration", "fileUploadIsRequired": "File upload is required", "knowledgeBaseConfiguration": "Knowledge Base Configuration", "placeholderKnowledgeBase": "Enter KnowledgeBase Name", "knowledgeBaseName": "KnowledgeBase Name", "description": "Description", "assignFilters": "Assign <PERSON>s", "organization": "Organization", "domain": "Domain", "project": "Project", "team": "Team", "fileName": "File Name", "fileSize": "File Size", "uploadDate": "Upload Date", "action": "Action", "fileTypeDocx": "docx", "fileTypePdf": "pdf", "fileTypePptx": "pptx", "uploadType": "Upload Type", "uploadNote": "Note: upload only .pdf, .txt, .docx, .pptx, .html, .xlsx files", "dragDropUpload": "Drag and Drop your File(s) Or Upload File(s)", "filters": "Filters", "default": "<PERSON><PERSON><PERSON>", "parentDoc": "Parent Doc", "loadingText": "Loading knowledge data...", "createKnowledgeBase": "Create Knowledge Base", "selectRetriever": "Select Retriever", "splitSize": "Split Size", "embeddingModel": "Choose Embedding Model", "selectEmbeddingModel": "Select Embedding Model", "parentSplitSize": "Parent Split Size", "childSplitSize": "Child Split Size", "uploadedKnowledgebase": "Uploaded Knowledgebase", "exit": "Exit", "update": "Update", "save": "Save", "noResults": "No knowledge bases found matching your criteria", "selectedFiles": "Selected Files", "noFilesUploaded": "No files uploaded yet.", "parentChildSplitValidation": "Parent split size must be greater than child split size.", "containerName": "Container Name", "accountName": "Account Name", "accountKey": "Account Key", "githubKey": "GitHub Key", "githubAccount": "<PERSON><PERSON><PERSON><PERSON> Account", "githubRepository": "GitHub Repository", "githubBranch": "GitHub Branch", "clientId": "Client ID", "clientSecret": "Client Secret", "tenantId": "Tenant ID", "siteName": "SharePoint Site Name", "folderPath": "SharePoint Folder Path", "emailId": "Email ID", "confluenceClientId": "Client ID", "confluenceClientSecret": "Client Secret", "apiToken": "API Token", "baseUrl": "Base URL", "spaceKey": "Space Key", "overlap": "Overlap", "schema": "<PERSON><PERSON><PERSON>", "dbHost": "DB Host", "dbPort": "DB Port", "dbUser": "DB User", "dbPassword": "DB Password", "dbName": "DB Name", "dbQuery": "DB Query", "configSource": "Config Source", "selectScheme": "Select Scheme", "placeholderDescription": "Enter knowledgeBase description", "placeholderContainerName": "Enter container name", "placeholderAccountName": "Enter account name", "placeholderAccountKey": "Enter account key", "placeholderGithubKey": "Enter github key", "placeholderGithubAccount": "Enter github account", "placeholderGithubRepository": "Enter github repository", "placeholderSiteName": "Enter sharepoint site name", "placeholderFolderPath": "Enter sharepoint folder path", "placeholderGithubBranch": "Enter github branch", "placeholderClientId": "Enter client ID", "placeholderClientSecret": "Enter client secret", "placeholderEmailId": "Enter email ID", "placeholderConfluenceClientId": "Enter client ID", "placeholderConfluenceClientSecret": "Enter client secret", "placeholderApiToken": "Enter API token", "placeholderBaseUrl": "Enter base URL", "placeholderTenantId": "Enter tenant ID", "placeholderHost": "Enter host", "placeholderPort": "Enter port", "placeholderSpaceKey": "Enter space key", "placeholderDbName": "Enter db name", "placeholderDbQuery": "Enter query", "placeholderPassword": "Enter password", "placeholderUser": "Enter user", "placeholderOverlap": "Enter overlap", "zeroKbFileError": "Cannot upload a file with 0KB size."}, "allowedFormats": ["pdf", "txt", "doc", "docx", "ppt", "pptx", "html", "xls", "xlsx"], "azureBlob": ["containerName", "accountName", "accountKey"], "github": ["gith<PERSON><PERSON><PERSON>", "gith<PERSON><PERSON><PERSON>unt", "githubRepo", "githubBranch"], "sharePoint": ["clientId", "clientSecret", "tenantId", "sharepointSiteName", "sharepointFolderPath"], "confluenceWiki": ["email", "clientId", "clientSecret", "apiToken", "baseUrl", "spaceKey", "overlap"], "database": ["scheme", "host", "port", "user", "password", "dbname", "query", "overlap"]}