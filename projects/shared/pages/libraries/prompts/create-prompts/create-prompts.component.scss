// Layout for role and dropdown side by side
.role-dropdown-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  margin-top: 8px;
}

// Match textbox and dropdown style
.role-dropdown-style {
  flex: 1 1 0;
}

.loading-overlay {
  // position: absolute;
  // top: 0;
  // left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.create-prompts-container {
  width: 100%;
  height: 100vh;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

.page-title {
  font-weight: 700;
  font-size: 24px;
  font-weight: bold;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 0;
  padding: 0;
  flex: 1 1 0;
  min-height: 0;
  overflow: hidden;
  height: 100%;
  border: 1px solid #e1e4e8;
  background: #ffffff;
}

.left-column,
.middle-column,
.chat-column {
  //padding: 20px;
  box-sizing: border-box;
}

.left-column {
  width: 340px;
  min-width: 60px;
  max-width: 340px;
  transition: width 0.3s cubic-bezier(0.4,0,0.2,1);
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: #f8f9fa;
  border-right: 1px solid #e1e4e8;
  display: flex;
  flex-direction: column;
}
.left-column.collapsed {
  width: 48px;
  min-width: 48px;
  max-width: 48px;
}
.collapse-icon {
  position: absolute;
  top: 16px;
  right: 8px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  cursor: pointer;
  z-index: 2;
  font-size: 18px;
  border: 1px solid #e1e4e8;
}
.left-column.collapsed .collapse-icon {
  right: 12px;
}
.left-column .card-content {
  flex: 1 1 0;
  min-height: 0;
  overflow-y: auto;
  padding: 1rem;
}

.middle-column {
  flex: 1 1 0;
  min-width: 0;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 0.5rem 1rem;;
}

.chat-column {
  width: 400px;
  min-width: 400px;
  background-color: #f8f8f8;
  border-left: 1px solid #ddd;
  display: flex;
  flex-direction: column;
}

.prompt_header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.freeform-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

::ng-deep ava-textarea.input-box {
  width: 100%;
  min-height: 200px;
  height: auto;
  resize: vertical;
}

:host ::ng-deep .ava-textarea--primary .ava-textarea__container {
  border-color: #e1e4e8 !important; // Your override color
}


@media (max-width: 600px) {
  ::ng-deep ava-textarea.input-box {
    min-height: 150px;
  }
}

@media (min-width: 1200px) {
  ::ng-deep ava-textarea.input-box {
    min-height: 250px;
  }
}

.regenerate-button-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
  margin-bottom: 0;
}

// .regenerate-button {
//   display: inline-flex;
//   align-items: center;
//   gap: 0px;
//   border-radius: 6px;
//   border: none;
//   cursor: pointer;
// }

.role-textbox {
  width: 40%;
  flex-shrink: 0;

}

.fields-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.fields-row1 {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  height: 10rem;
  border-bottom: 1px solid #e1e4e8;
}

.add-consideration {
  margin-top: 20px;
  height: 10rem;
  padding-top: 20px;
  border-top: 1px solid #e1e4e8;

}


.field-col {
  flex: 1 1 45%;
  min-width: 400px;
}

.optional-sections {
  margin-top: 0;

  ava-accordion ::ng-deep .accordion-container .accordion-body {
    padding: 0%;
  }

  // ava-accordion ::ng-deep .accordion-container .accordion-content {
  //   //padding-top: 26px;
  // }
}

.form-container{
  overflow-y:auto;
  scroll-width:none;
}

.accordion-section {
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  margin-bottom: 20px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.playground-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.chat-column-content {
  flex: 1 1 0;
  min-height: 0;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.chat-column-content::-webkit-scrollbar {
  display: none;
}

.tabs-wrapper {
  display: flex;
  justify-content: center;
}

.tab-heading {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.tab-container {
  margin-bottom: 20px;
}

/* ✅ Custom Pill Tabs Styles */
.pill-tabs-container {
  display: inline-flex;
  border-radius: 999px;
  overflow: hidden;
  border: 1px solid #e1e4e8;
  background-color: #f8f9fa;
  padding: 4px;
  gap: 0;
}

.pill-tab-button {
  border-radius: 999px;
  margin: 0;
  border: none;
  font-weight: 500;
  padding: 10px 20px;
  background-color: transparent;
  color: #6c757d;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 14px;
  min-width: 80px;
  text-align: center;
}

.pill-tab-button:hover:not(.active) {
  background-color: rgba(101, 102, 205, 0.05);
  color: #6566CD;
}

.pill-tab-button.active {
  background-color: #6566CD;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(101, 102, 205, 0.3);
}

.item-icon {
  display: none !important; // Hide the icon
}

.example-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .example-group {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 1rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e1e4e8;
    position: relative;

    .field-col {
      flex: 1 1 45%;
    }

    .example-remove {
      margin-left: auto;
      margin-top: auto;
    }
  }

  .example-actions {
    display: flex;
    justify-content: flex-start; // Add Example left aligned
    margin-top: 1rem;
  }
}

.examples-accordion-wrapper {
  margin-top: 1rem;
}

.examples-accordion-wrapper ::ng-deep .accordion-header {
  min-width: 100% !important;
}

:host ::ng-deep ava-accordion .accordion-container {
  max-width: none !important;
  width: 100% !important; // reinforces full width
}

:host ::ng-deep ava-accordion .accordion-container .accordion-content {
  max-height: 100%;
  overflow-y: auto;
}

::ng-deep #description .ava-textarea__label {
  margin-top: 16px;
}

.left-header {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e1e4e8;
  z-index: 2;
}

.right-header {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e1e4e8;
  z-index: 2;
}

.tab-content {
  flex: 1 1 0;
  min-height: 0;
  //overflow-y: auto;
  //scrollbar-width: none;
  //-ms-overflow-style: none;
}
.tab-content::-webkit-scrollbar {
  display: none;
}